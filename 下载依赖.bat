@echo off
chcp 65001 >nul
echo ========================================
echo Clash配置生成工具 - 依赖下载脚本
echo ========================================
echo.
echo 正在下载外部依赖文件...
echo 这可能需要几分钟时间，请耐心等待...
echo.

node download-assets.js

if %errorlevel% equ 0 (
    echo.
    echo ========================================
    echo 下载完成！
    echo ========================================
    echo.
    echo 现在可以启动服务器了：
    echo   npm start
    echo 或者：
    echo   node src/index.js
    echo.
    echo 启动后可通过以下地址访问：
    echo   本机：http://localhost:3000
    echo   局域网：http://[您的IP]:3000
    echo.
) else (
    echo.
    echo ========================================
    echo 下载失败！
    echo ========================================
    echo.
    echo 请检查：
    echo 1. 网络连接是否正常
    echo 2. Node.js 是否已安装
    echo 3. 是否有防火墙阻止下载
    echo.
)

pause
