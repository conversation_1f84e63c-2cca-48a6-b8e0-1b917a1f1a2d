# 定时自动获取节点并生成配置功能说明

## 🕒 功能概述

新增的定时任务功能可以自动在指定时间获取节点并生成配置文件，无需手动操作。

## ✨ 主要特性

### 1. 定时自动执行
- **默认时间**：每天早上8:00自动执行
- **可自定义**：支持设置任意时间（24小时制）
- **智能调度**：使用cron表达式精确控制执行时间

### 2. 状态监控
- **实时状态**：显示定时任务当前状态（未启用/已启用/运行中）
- **执行记录**：显示上次执行时间和结果
- **下次预告**：显示下次执行的预计时间

### 3. 配置生成时间显示
- **实时更新**：每次生成配置后显示最新的生成时间
- **历史记录**：页面加载时显示上次配置文件的生成时间
- **格式统一**：使用中文本地化时间格式

## 🎛️ 使用方法

### 启用定时任务
1. 点击"定时任务"按钮展开配置面板
2. 开启"启用定时自动生成配置"开关
3. 设置执行时间（默认08:00）
4. 点击"保存设置"按钮

### 立即执行
- 点击"立即执行"按钮可以手动触发一次配置生成
- 不影响定时任务的正常调度

### 查看状态
- **状态**：显示定时任务是否启用
- **下次执行**：显示下次自动执行的时间
- **上次执行**：显示上次执行的时间和结果

## 🔧 技术实现

### 服务器端
- 使用 `node-cron` 库实现定时任务调度
- 支持中国时区（Asia/Shanghai）
- 配置持久化存储在 `configs/auto_task_config.json`

### 前端界面
- Bootstrap折叠面板设计
- 实时状态更新
- 友好的用户交互体验

### API端点
- `GET /api/auto-task/status` - 获取定时任务状态
- `POST /api/auto-task/config` - 配置定时任务
- `POST /api/auto-task/run-now` - 立即执行任务

## 📋 配置文件格式

定时任务配置保存在 `configs/auto_task_config.json`：

```json
{
  "enabled": true,
  "time": "08:00",
  "lastExecution": "2025-01-01T08:00:00.000Z",
  "nextExecution": "2025-01-02T08:00:00.000Z"
}
```

## 🛡️ 安全特性

### 错误处理
- 网络异常自动重试
- 配置错误友好提示
- 执行失败详细日志

### 数据保护
- 配置文件自动备份
- 执行状态实时同步
- 异常情况自动恢复

## 📊 监控日志

### 服务器日志
```
启动定时任务，执行时间: 每天 08:00 (cron: 0 0 8 * * *)
开始执行定时任务：自动获取节点并生成配置
定时任务执行成功
```

### 前端状态
- 成功：显示绿色成功消息
- 失败：显示红色错误消息
- 进行中：显示蓝色进度提示

## 🔄 自动恢复

### 服务器重启
- 自动加载保存的定时任务配置
- 重新计算下次执行时间
- 恢复定时任务调度

### 配置同步
- 所有用户共享同一个定时任务配置
- 配置更改实时生效
- 状态信息自动同步

## 💡 使用建议

1. **合理设置时间**：建议设置在网络较好的时间段
2. **定期检查**：定期查看执行状态确保正常运行
3. **备用方案**：保留手动生成配置的习惯作为备用
4. **监控日志**：关注服务器日志了解执行情况

---

**🎉 现在您可以享受全自动的配置生成服务了！**
