# Clash配置生成工具 - 局域网部署指南

本项目已经配置为支持局域网访问，所有外部依赖文件都可以下载到本地。

## 快速开始

### 1. 下载外部依赖文件

运行以下命令下载所有外部依赖文件到本地：

```bash
node download-assets.js
```

这个脚本会下载以下文件：
- Bootstrap CSS 和 JS
- Bootstrap Icons 字体文件
- CodeMirror 编辑器库

下载完成后，所有文件将保存在 `public/libs/` 目录下。

### 2. 启动服务器

```bash
npm start
# 或者
node src/index.js
```

### 3. 局域网访问

服务器启动后，您可以通过以下方式访问：

- 本机访问：`http://localhost:3000`
- 局域网访问：`http://[您的IP地址]:3000`

例如：`http://*************:3000`

## 目录结构

```
project/
├── public/
│   ├── libs/                    # 本地依赖文件
│   │   ├── bootstrap/
│   │   │   ├── css/
│   │   │   │   └── bootstrap.min.css
│   │   │   └── js/
│   │   │       └── bootstrap.bundle.min.js
│   │   ├── bootstrap-icons/
│   │   │   ├── bootstrap-icons.css
│   │   │   └── fonts/
│   │   │       ├── bootstrap-icons.woff2
│   │   │       └── bootstrap-icons.woff
│   │   └── codemirror/
│   │       ├── css/
│   │       │   ├── codemirror.min.css
│   │       │   ├── monokai.min.css
│   │       │   └── simplescrollbars.min.css
│   │       └── js/
│   │           ├── codemirror.min.js
│   │           ├── yaml.min.js
│   │           ├── active-line.min.js
│   │           └── simplescrollbars.min.js
│   ├── css/
│   ├── js/
│   └── index.html
├── src/
├── configs/
├── download-assets.js           # 依赖下载脚本
└── package.json
```

## 注意事项

1. **首次部署**：请确保先运行 `node download-assets.js` 下载所有依赖文件
2. **网络要求**：下载脚本需要互联网连接，但下载完成后项目可以完全离线运行
3. **防火墙设置**：确保服务器的3000端口在防火墙中已开放
4. **IP地址获取**：
   - Windows: `ipconfig`
   - Linux/Mac: `ifconfig` 或 `ip addr`

## 故障排除

### 下载失败
如果下载过程中出现错误，请检查：
1. 网络连接是否正常
2. 是否有防火墙阻止下载
3. 重新运行下载脚本

### 局域网无法访问
1. 检查服务器IP地址是否正确
2. 确认防火墙设置
3. 确认设备在同一局域网内

### 样式或功能异常
1. 确认所有依赖文件已正确下载
2. 检查浏览器控制台是否有错误信息
3. 清除浏览器缓存后重试

## 更新依赖

如果需要更新依赖版本，请：
1. 修改 `download-assets.js` 中的版本号
2. 删除 `public/libs/` 目录
3. 重新运行下载脚本

## 技术支持

如有问题，请检查：
1. Node.js 版本是否兼容
2. 所有依赖是否正确安装
3. 端口是否被占用
