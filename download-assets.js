const fs = require('fs');
const path = require('path');
const https = require('https');

// 创建目录的函数
function ensureDir(dirPath) {
    if (!fs.existsSync(dirPath)) {
        fs.mkdirSync(dirPath, { recursive: true });
        console.log(`创建目录: ${dirPath}`);
    }
}

// 下载文件的函数
function downloadFile(url, filePath) {
    return new Promise((resolve, reject) => {
        console.log(`正在下载: ${url}`);

        const file = fs.createWriteStream(filePath);

        https.get(url, (response) => {
            // 处理重定向
            if (response.statusCode === 301 || response.statusCode === 302) {
                file.close();
                fs.unlinkSync(filePath);
                return downloadFile(response.headers.location, filePath)
                    .then(resolve)
                    .catch(reject);
            }

            if (response.statusCode !== 200) {
                file.close();
                fs.unlinkSync(filePath);
                reject(new Error(`下载失败: ${response.statusCode} ${response.statusMessage}`));
                return;
            }

            response.pipe(file);

            file.on('finish', () => {
                file.close();
                console.log(`下载完成: ${path.basename(filePath)}`);
                resolve();
            });

            file.on('error', (err) => {
                file.close();
                fs.unlinkSync(filePath);
                reject(err);
            });
        }).on('error', (err) => {
            file.close();
            if (fs.existsSync(filePath)) {
                fs.unlinkSync(filePath);
            }
            reject(err);
        });
    });
}

// 修改CSS文件中的字体路径
function fixBootstrapIconsCSS(cssPath) {
    try {
        let cssContent = fs.readFileSync(cssPath, 'utf8');

        // 替换字体文件路径
        cssContent = cssContent.replace(
            /url\("\.\/fonts\/bootstrap-icons\.woff2\?[^"]*"\)/g,
            'url("./fonts/bootstrap-icons.woff2")'
        );
        cssContent = cssContent.replace(
            /url\("\.\/fonts\/bootstrap-icons\.woff\?[^"]*"\)/g,
            'url("./fonts/bootstrap-icons.woff")'
        );

        fs.writeFileSync(cssPath, cssContent, 'utf8');
        console.log('Bootstrap Icons CSS 路径已修复');
    } catch (error) {
        console.error('修复 Bootstrap Icons CSS 失败:', error);
    }
}

// 要下载的文件列表
const downloads = [
    // Bootstrap CSS
    {
        url: 'https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css',
        path: 'public/libs/bootstrap/css/bootstrap.min.css'
    },
    // Bootstrap JS
    {
        url: 'https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js',
        path: 'public/libs/bootstrap/js/bootstrap.bundle.min.js'
    },
    // Bootstrap Icons CSS
    {
        url: 'https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css',
        path: 'public/libs/bootstrap-icons/bootstrap-icons.css'
    },
    // Bootstrap Icons 字体文件
    {
        url: 'https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/fonts/bootstrap-icons.woff2',
        path: 'public/libs/bootstrap-icons/fonts/bootstrap-icons.woff2'
    },
    {
        url: 'https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/fonts/bootstrap-icons.woff',
        path: 'public/libs/bootstrap-icons/fonts/bootstrap-icons.woff'
    },
    // CodeMirror CSS
    {
        url: 'https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.15/codemirror.min.css',
        path: 'public/libs/codemirror/css/codemirror.min.css'
    },
    {
        url: 'https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.15/theme/monokai.min.css',
        path: 'public/libs/codemirror/css/monokai.min.css'
    },
    {
        url: 'https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.15/addon/scroll/simplescrollbars.min.css',
        path: 'public/libs/codemirror/css/simplescrollbars.min.css'
    },
    // CodeMirror JS
    {
        url: 'https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.15/codemirror.min.js',
        path: 'public/libs/codemirror/js/codemirror.min.js'
    },
    {
        url: 'https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.15/mode/yaml/yaml.min.js',
        path: 'public/libs/codemirror/js/yaml.min.js'
    },
    {
        url: 'https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.15/addon/selection/active-line.min.js',
        path: 'public/libs/codemirror/js/active-line.min.js'
    },
    {
        url: 'https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.15/addon/scroll/simplescrollbars.min.js',
        path: 'public/libs/codemirror/js/simplescrollbars.min.js'
    }
];

// 主下载函数
async function downloadAssets() {
    console.log('开始下载外部资源文件...\n');

    try {
        // 创建必要的目录
        ensureDir('public/libs/bootstrap/css');
        ensureDir('public/libs/bootstrap/js');
        ensureDir('public/libs/bootstrap-icons/fonts');
        ensureDir('public/libs/codemirror/css');
        ensureDir('public/libs/codemirror/js');

        // 下载所有文件
        for (const download of downloads) {
            try {
                await downloadFile(download.url, download.path);
            } catch (error) {
                console.error(`下载失败 ${download.url}:`, error.message);
                process.exit(1);
            }
        }

        // 修复 Bootstrap Icons CSS 中的字体路径
        fixBootstrapIconsCSS('public/libs/bootstrap-icons/bootstrap-icons.css');

        console.log('\n所有文件下载完成！');
        console.log('现在可以运行项目，支持局域网访问。');

    } catch (error) {
        console.error('下载过程中出错:', error);
        process.exit(1);
    }
}

// 运行下载
downloadAssets();
