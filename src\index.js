const express = require('express');
const cors = require('cors');
const bodyParser = require('body-parser');
const path = require('path');
const fs = require('fs');
const cron = require('node-cron');

const app = express();
const PORT = process.env.PORT || 3000;

// 定时任务相关变量
let autoTask = null;
let autoTaskConfig = {
    enabled: false,
    time: '08:00',
    lastExecution: null,
    nextExecution: null
};

// 中间件配置
app.use(cors({
    origin: '*', // 允许任何源
    methods: ['GET', 'POST'],
    allowedHeaders: ['Content-Type', 'Authorization']
}));

// 增加请求体大小限制
app.use(bodyParser.json({ limit: '50mb' }));
app.use(bodyParser.urlencoded({ limit: '50mb', extended: true }));

app.use(express.static(path.join(__dirname, '../public'))); // 前端静态文件

// 禁用缓存
app.use((req, res, next) => {
    res.header('Cache-Control', 'no-store, no-cache, must-revalidate, proxy-revalidate');
    res.header('Pragma', 'no-cache');
    res.header('Expires', '0');
    next();
});

// 开发环境日志
app.use((req, res, next) => {
    console.log(`${new Date().toISOString()} - ${req.method} ${req.url}`);
    next();
});

// 确保配置目录存在
const configDir = path.join(__dirname, '../configs');
if (!fs.existsSync(configDir)) {
    fs.mkdirSync(configDir, { recursive: true });
}

// API路由
// 1. 获取节点数据
app.post('/api/fetch-nodes', async (req, res) => {
    try {
        // 此函数将在utils中实现
        const { fetchProxiesFromSubscription } = require('./utils/subscription');
        const { urls } = req.body;

        if (!urls || !Array.isArray(urls) || urls.length === 0) {
            return res.status(400).json({ error: '请提供有效的订阅URL列表' });
        }

        const results = [];
        for (const url of urls) {
            if (!url.trim()) continue;
            try {
                const nodeData = await fetchProxiesFromSubscription(url);
                results.push({
                    url,
                    success: true,
                    proxies: nodeData
                });
            } catch (error) {
                results.push({
                    url,
                    success: false,
                    error: error.message
                });
            }
        }

        res.json({ results });
    } catch (error) {
        console.error('获取节点失败:', error);
        res.status(500).json({ error: '处理订阅时出错: ' + error.message });
    }
});

// 2. 生成SLEEP2025.yaml配置文件
app.post('/api/generate', async (req, res) => {
    try {
        const { generateConfig } = require('./utils/generator');
        const { header, rules, enabledSubscriptions, customNodes } = req.body;

        if (!enabledSubscriptions || !Array.isArray(enabledSubscriptions)) {
            return res.status(400).json({ error: '请提供有效的订阅列表' });
        }

        const configPath = await generateConfig(header, rules, enabledSubscriptions, customNodes);
        res.json({ success: true, configPath });
    } catch (error) {
        console.error('生成配置失败:', error);
        res.status(500).json({ error: '生成配置文件时出错: ' + error.message });
    }
});

// 3. 订阅链接转换
app.post('/api/convert', async (req, res) => {
    try {
        const { convertSubscription } = require('./utils/converter');
        const { url } = req.body;

        if (!url) {
            return res.status(400).json({ error: '订阅URL不能为空' });
        }

        const result = await convertSubscription(url, req);
        res.json(result);
    } catch (error) {
        console.error('转换订阅失败:', error);
        res.status(500).json({ error: '转换订阅时出错: ' + error.message });
    }
});

// 4. 获取生成的配置文件
app.get('/api/config/:configName', (req, res) => {
    const configName = req.params.configName;
    if (!/^[a-zA-Z0-9_.-]+$/.test(configName)) { // 文件名安全检查
        return res.status(400).send('无效的配置名称');
    }

    const filePath = path.join(__dirname, '../configs', `${configName}.yaml`);
    if (fs.existsSync(filePath)) {
        res.setHeader('Content-Disposition', `attachment; filename=${configName}.yaml`);
        res.setHeader('Content-Type', 'text/yaml; charset=utf-8');
        fs.createReadStream(filePath).pipe(res);
    } else {
        res.status(404).send('找不到配置文件');
    }
});

// 5. 检查配置文件是否存在
app.get('/api/check-config-exists', (req, res) => {
    try {
        const configDir = path.join(__dirname, '../configs');
        const configPath = path.join(configDir, 'SLEEP2025.yaml');

        const exists = fs.existsSync(configPath);
        const configUrl = exists ? `${req.protocol}://${req.get('host')}/api/config/SLEEP2025` : '';

        res.json({
            exists,
            configPath: exists ? 'SLEEP2025' : '',
            configUrl,
            lastModified: exists ? fs.statSync(configPath).mtime.toISOString() : null
        });
    } catch (error) {
        console.error('检查配置文件失败:', error);
        res.status(500).json({
            error: '检查配置文件失败: ' + error.message,
            exists: false,
            configPath: '',
            configUrl: ''
        });
    }
});

// 添加固定的路由，避免使用通配符路由
app.get('/', (req, res) => {
    res.sendFile(path.join(__dirname, '../public/index.html'));
});

// 添加favicon路由
app.get('/favicon.ico', (req, res) => {
    res.sendFile(path.join(__dirname, '../public/favicon.ico'));
});

// 保存配置API
app.post('/api/save-config', (req, res) => {
    try {
        const { subscriptions, header, userRules, existingRules, customNodes } = req.body;

        if (!subscriptions || !Array.isArray(subscriptions)) {
            return res.status(400).json({ error: '订阅数据格式不正确' });
        }

        // 创建配置对象
        const config = {
            subscriptions,
            header: header || '',
            userRules: userRules || '',
            existingRules: existingRules || '',
            customNodes: customNodes || [],
            lastUpdated: new Date().toISOString()
        };

        // 确保配置目录存在
        const configDir = path.join(__dirname, '../configs');
        if (!fs.existsSync(configDir)) {
            console.log(`创建配置目录: ${configDir}`);
            try {
                fs.mkdirSync(configDir, { recursive: true });
            } catch (mkdirError) {
                console.error(`创建配置目录失败: ${mkdirError.message}`);
                return res.status(500).json({
                    error: `创建配置目录失败: ${mkdirError.message}`,
                    details: {
                        configDir,
                        errorCode: mkdirError.code,
                        errorStack: mkdirError.stack
                    }
                });
            }
        }

        // 保存到文件
        const configPath = path.join(configDir, 'user_config.json');
        console.log(`保存配置到: ${configPath}`);

        try {
            // 检查文件是否可写
            if (fs.existsSync(configPath)) {
                fs.accessSync(configPath, fs.constants.W_OK);
                console.log('配置文件存在且可写');
            }

            fs.writeFileSync(configPath, JSON.stringify(config, null, 2), 'utf8');
            console.log('配置已成功写入文件');

            res.json({
                success: true,
                message: '配置已保存',
                path: configPath,
                timestamp: new Date().toISOString()
            });
        } catch (writeError) {
            console.error(`写入配置文件失败: ${writeError.message}`);
            return res.status(500).json({
                error: `写入配置文件失败: ${writeError.message}`,
                details: {
                    configPath,
                    errorCode: writeError.code,
                    errorStack: writeError.stack
                }
            });
        }
    } catch (error) {
        console.error('保存配置失败:', error);
        res.status(500).json({
            error: '保存配置失败: ' + error.message,
            stack: error.stack
        });
    }
});

// 加载配置API
app.get('/api/load-config', (req, res) => {
    try {
        const configDir = path.join(__dirname, '../configs');
        const configPath = path.join(configDir, 'user_config.json');

        console.log(`尝试加载配置: ${configPath}`);

        // 检查配置目录是否存在
        if (!fs.existsSync(configDir)) {
            console.log(`配置目录不存在: ${configDir}，创建目录`);
            try {
                fs.mkdirSync(configDir, { recursive: true });
                console.log(`配置目录已创建: ${configDir}`);
            } catch (mkdirError) {
                console.error(`创建配置目录失败: ${mkdirError.message}`);
                // 返回默认配置
                return res.json({
                    subscriptions: [],
                    header: '',
                    userRules: '',
                    existingRules: '',
                    customNodes: [],
                    lastUpdated: null,
                    error: `创建配置目录失败: ${mkdirError.message}`
                });
            }
        }

        // 检查配置文件是否存在
        if (!fs.existsSync(configPath)) {
            console.log(`配置文件不存在: ${configPath}，返回默认配置`);
            // 返回默认配置
            return res.json({
                subscriptions: [],
                header: '',
                userRules: '',
                existingRules: '',
                customNodes: [],
                lastUpdated: null,
                message: '使用默认配置（配置文件不存在）'
            });
        }

        try {
            // 检查文件是否可读
            fs.accessSync(configPath, fs.constants.R_OK);
            console.log('配置文件存在且可读');

            // 读取配置文件
            const configData = fs.readFileSync(configPath, 'utf8');
            console.log(`配置文件内容长度: ${configData.length} 字节`);

            if (!configData || configData.trim() === '') {
                console.log('配置文件为空，返回默认配置');
                return res.json({
                    subscriptions: [],
                    header: '',
                    userRules: '',
                    existingRules: '',
                    customNodes: [],
                    lastUpdated: null,
                    message: '使用默认配置（配置文件为空）'
                });
            }

            try {
                const config = JSON.parse(configData);
                console.log('配置文件解析成功');

                res.json({
                    ...config,
                    message: '配置加载成功',
                    timestamp: new Date().toISOString()
                });
            } catch (parseError) {
                console.error(`解析配置文件失败: ${parseError.message}`);
                return res.status(500).json({
                    error: `解析配置文件失败: ${parseError.message}`,
                    details: {
                        configPath,
                        errorMessage: parseError.message,
                        configDataPreview: configData.substring(0, 100) + '...'
                    },
                    subscriptions: [],
                    header: '',
                    userRules: '',
                    existingRules: '',
                    lastUpdated: null
                });
            }
        } catch (readError) {
            console.error(`读取配置文件失败: ${readError.message}`);
            return res.status(500).json({
                error: `读取配置文件失败: ${readError.message}`,
                details: {
                    configPath,
                    errorCode: readError.code,
                    errorStack: readError.stack
                },
                subscriptions: [],
                header: '',
                userRules: '',
                existingRules: '',
                lastUpdated: null
            });
        }
    } catch (error) {
        console.error('加载配置失败:', error);
        res.status(500).json({
            error: '加载配置失败: ' + error.message,
            stack: error.stack,
            subscriptions: [],
            header: '',
            userRules: '',
            existingRules: '',
            lastUpdated: null
        });
    }
});

// 添加文件系统检查API
app.get('/api/check-filesystem', (req, res) => {
    try {
        const results = {
            checks: [],
            summary: {
                success: true,
                message: '文件系统检查通过'
            }
        };

        // 检查1: 检查configs目录
        const configDir = path.join(__dirname, '../configs');
        let configDirExists = fs.existsSync(configDir);
        results.checks.push({
            name: '配置目录存在',
            path: configDir,
            success: configDirExists,
            message: configDirExists ? '配置目录存在' : '配置目录不存在'
        });

        // 如果目录不存在，尝试创建
        if (!configDirExists) {
            try {
                fs.mkdirSync(configDir, { recursive: true });
                configDirExists = fs.existsSync(configDir);
                results.checks.push({
                    name: '创建配置目录',
                    path: configDir,
                    success: configDirExists,
                    message: configDirExists ? '配置目录创建成功' : '配置目录创建失败'
                });
            } catch (mkdirError) {
                results.checks.push({
                    name: '创建配置目录',
                    path: configDir,
                    success: false,
                    message: `创建配置目录失败: ${mkdirError.message}`,
                    error: mkdirError.stack
                });
                results.summary.success = false;
                results.summary.message = '文件系统检查失败: 无法创建配置目录';
            }
        }

        // 检查2: 检查配置文件
        if (configDirExists) {
            const configPath = path.join(configDir, 'user_config.json');
            const configFileExists = fs.existsSync(configPath);
            results.checks.push({
                name: '配置文件存在',
                path: configPath,
                success: true,
                message: configFileExists ? '配置文件存在' : '配置文件不存在（这是正常的，将在保存时创建）'
            });

            // 检查3: 检查目录写入权限
            try {
                const testFile = path.join(configDir, `test_${Date.now()}.txt`);
                fs.writeFileSync(testFile, 'test', 'utf8');
                const testFileExists = fs.existsSync(testFile);
                results.checks.push({
                    name: '目录写入权限',
                    path: configDir,
                    success: testFileExists,
                    message: testFileExists ? '目录可写' : '目录写入测试失败'
                });

                // 清理测试文件
                if (testFileExists) {
                    fs.unlinkSync(testFile);
                    const testFileRemoved = !fs.existsSync(testFile);
                    results.checks.push({
                        name: '目录删除权限',
                        path: configDir,
                        success: testFileRemoved,
                        message: testFileRemoved ? '目录可删除文件' : '目录删除文件测试失败'
                    });
                }
            } catch (writeError) {
                results.checks.push({
                    name: '目录写入权限',
                    path: configDir,
                    success: false,
                    message: `目录写入测试失败: ${writeError.message}`,
                    error: writeError.stack
                });
                results.summary.success = false;
                results.summary.message = '文件系统检查失败: 目录没有写入权限';
            }

            // 检查4: 如果配置文件存在，检查读取权限
            if (configFileExists) {
                try {
                    fs.accessSync(configPath, fs.constants.R_OK);
                    results.checks.push({
                        name: '配置文件读取权限',
                        path: configPath,
                        success: true,
                        message: '配置文件可读'
                    });

                    // 检查5: 检查配置文件写入权限
                    try {
                        fs.accessSync(configPath, fs.constants.W_OK);
                        results.checks.push({
                            name: '配置文件写入权限',
                            path: configPath,
                            success: true,
                            message: '配置文件可写'
                        });
                    } catch (writeError) {
                        results.checks.push({
                            name: '配置文件写入权限',
                            path: configPath,
                            success: false,
                            message: `配置文件写入权限检查失败: ${writeError.message}`,
                            error: writeError.stack
                        });
                        results.summary.success = false;
                        results.summary.message = '文件系统检查失败: 配置文件没有写入权限';
                    }
                } catch (readError) {
                    results.checks.push({
                        name: '配置文件读取权限',
                        path: configPath,
                        success: false,
                        message: `配置文件读取权限检查失败: ${readError.message}`,
                        error: readError.stack
                    });
                    results.summary.success = false;
                    results.summary.message = '文件系统检查失败: 配置文件没有读取权限';
                }
            }
        }

        // 检查6: 获取进程信息
        results.process = {
            pid: process.pid,
            uid: process.getuid ? process.getuid() : 'N/A',
            gid: process.getgid ? process.getgid() : 'N/A',
            cwd: process.cwd(),
            platform: process.platform,
            version: process.version,
            env: {
                NODE_ENV: process.env.NODE_ENV,
                PATH: process.env.PATH ? process.env.PATH.split(path.delimiter) : 'N/A'
            }
        };

        res.json(results);
    } catch (error) {
        console.error('文件系统检查失败:', error);
        res.status(500).json({
            error: '文件系统检查失败: ' + error.message,
            stack: error.stack
        });
    }
});

// 定时任务相关函数
function calculateNextExecution(timeStr) {
    const [hours, minutes] = timeStr.split(':').map(Number);
    const now = new Date();
    const next = new Date();

    next.setHours(hours, minutes, 0, 0);

    // 如果今天的时间已经过了，设置为明天
    if (next <= now) {
        next.setDate(next.getDate() + 1);
    }

    return next;
}

function formatDateTime(date) {
    if (!date) return '-';
    return new Date(date).toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
    });
}

async function executeAutoTask() {
    console.log('开始执行定时任务：自动获取节点并生成配置');

    try {
        // 获取当前配置
        const configPath = path.join(__dirname, '../configs/user_config.json');
        let config = {
            subscriptions: [],
            header: '',
            userRules: '',
            existingRules: '',
            customNodes: []
        };

        if (fs.existsSync(configPath)) {
            const configData = fs.readFileSync(configPath, 'utf8');
            config = JSON.parse(configData);
        }

        // 获取启用的订阅
        const enabledSubscriptions = config.subscriptions
            .filter(sub => sub.enabled && sub.url)
            .map(sub => sub.url);

        if (enabledSubscriptions.length === 0) {
            console.log('定时任务：没有启用的订阅，跳过执行');
            return { success: false, message: '没有启用的订阅' };
        }

        // 生成配置
        const { generateConfig } = require('./utils/generator');
        const rules = {
            userCustomRules: config.userRules || '',
            existingRules: config.existingRules || ''
        };

        const configFileName = await generateConfig(config.header || '', rules, enabledSubscriptions, config.customNodes || []);

        // 更新执行时间
        autoTaskConfig.lastExecution = new Date().toISOString();
        autoTaskConfig.nextExecution = calculateNextExecution(autoTaskConfig.time).toISOString();

        // 保存定时任务配置
        saveAutoTaskConfig();

        console.log('定时任务执行成功');
        return { success: true, message: '配置生成成功', configFileName };

    } catch (error) {
        console.error('定时任务执行失败:', error);
        autoTaskConfig.lastExecution = new Date().toISOString();
        autoTaskConfig.nextExecution = calculateNextExecution(autoTaskConfig.time).toISOString();
        saveAutoTaskConfig();

        return { success: false, message: error.message };
    }
}

function startAutoTask() {
    if (autoTask) {
        autoTask.stop();
        autoTask = null;
    }

    if (!autoTaskConfig.enabled) {
        return;
    }

    const [hours, minutes] = autoTaskConfig.time.split(':').map(Number);
    const cronExpression = `0 ${minutes} ${hours} * * *`;

    console.log(`启动定时任务，执行时间: 每天 ${autoTaskConfig.time} (cron: ${cronExpression})`);

    autoTask = cron.schedule(cronExpression, executeAutoTask, {
        scheduled: true,
        timezone: 'Asia/Shanghai'
    });

    // 计算下次执行时间
    autoTaskConfig.nextExecution = calculateNextExecution(autoTaskConfig.time).toISOString();
}

function stopAutoTask() {
    if (autoTask) {
        autoTask.stop();
        autoTask = null;
        console.log('定时任务已停止');
    }
    autoTaskConfig.nextExecution = null;
}

function loadAutoTaskConfig() {
    try {
        const configPath = path.join(__dirname, '../configs/auto_task_config.json');
        if (fs.existsSync(configPath)) {
            const data = fs.readFileSync(configPath, 'utf8');
            autoTaskConfig = { ...autoTaskConfig, ...JSON.parse(data) };
            console.log('定时任务配置已加载:', autoTaskConfig);

            if (autoTaskConfig.enabled) {
                startAutoTask();
            }
        }
    } catch (error) {
        console.error('加载定时任务配置失败:', error);
    }
}

function saveAutoTaskConfig() {
    try {
        const configPath = path.join(__dirname, '../configs/auto_task_config.json');
        fs.writeFileSync(configPath, JSON.stringify(autoTaskConfig, null, 2), 'utf8');
        console.log('定时任务配置已保存');
    } catch (error) {
        console.error('保存定时任务配置失败:', error);
    }
}

// 定时任务API端点
app.get('/api/auto-task/status', (req, res) => {
    res.json({
        ...autoTaskConfig,
        isRunning: autoTask ? autoTask.running : false,
        lastExecutionFormatted: formatDateTime(autoTaskConfig.lastExecution),
        nextExecutionFormatted: formatDateTime(autoTaskConfig.nextExecution)
    });
});

app.post('/api/auto-task/config', (req, res) => {
    try {
        const { enabled, time } = req.body;

        if (typeof enabled !== 'boolean') {
            return res.status(400).json({ error: 'enabled 必须是布尔值' });
        }

        if (time && !/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/.test(time)) {
            return res.status(400).json({ error: '时间格式不正确，应为 HH:MM' });
        }

        autoTaskConfig.enabled = enabled;
        if (time) {
            autoTaskConfig.time = time;
        }

        if (enabled) {
            startAutoTask();
        } else {
            stopAutoTask();
        }

        saveAutoTaskConfig();

        res.json({
            success: true,
            message: enabled ? '定时任务已启用' : '定时任务已禁用',
            config: autoTaskConfig
        });

    } catch (error) {
        console.error('配置定时任务失败:', error);
        res.status(500).json({ error: '配置定时任务失败: ' + error.message });
    }
});

app.post('/api/auto-task/run-now', async (req, res) => {
    try {
        const result = await executeAutoTask();
        res.json(result);
    } catch (error) {
        console.error('立即执行定时任务失败:', error);
        res.status(500).json({ error: '执行失败: ' + error.message });
    }
});

// 启动服务器
app.listen(PORT, () => {
    console.log(`服务器已启动，监听端口: ${PORT}`);
    console.log(`访问 http://localhost:${PORT} 查看应用`);

    // 启动时检查文件系统
    const configDir = path.join(__dirname, '../configs');
    console.log(`检查配置目录: ${configDir}`);

    if (!fs.existsSync(configDir)) {
        console.log('配置目录不存在，尝试创建...');
        try {
            fs.mkdirSync(configDir, { recursive: true });
            console.log('配置目录创建成功');
        } catch (error) {
            console.error('创建配置目录失败:', error);
        }
    } else {
        console.log('配置目录已存在');
    }

    // 检查配置目录权限
    try {
        const testFile = path.join(configDir, `test_${Date.now()}.txt`);
        fs.writeFileSync(testFile, 'test', 'utf8');
        console.log('配置目录写入测试成功');

        fs.unlinkSync(testFile);
        console.log('配置目录删除测试成功');
    } catch (error) {
        console.error('配置目录权限测试失败:', error);
    }

    // 加载定时任务配置
    loadAutoTaskConfig();
});