# Clash配置生成工具 - 完整部署指南

## 🚀 快速部署（推荐）

### 方法一：使用npm脚本（推荐）
```bash
# 1. 安装依赖
npm install

# 2. 下载外部资源文件
npm run setup

# 3. 启动服务器
npm start
```

### 方法二：使用批处理文件（Windows）
```bash
# 1. 安装依赖
npm install

# 2. 双击运行 "下载依赖.bat" 文件

# 3. 启动服务器
npm start
```

### 方法三：使用Shell脚本（Linux/Mac）
```bash
# 1. 安装依赖
npm install

# 2. 运行下载脚本
./download-deps.sh

# 3. 启动服务器
npm start
```

## 📁 项目结构

```
clash-config-tool/
├── public/
│   ├── libs/                    # 📦 本地依赖文件（自动下载）
│   │   ├── bootstrap/           # Bootstrap框架
│   │   ├── bootstrap-icons/     # Bootstrap图标
│   │   └── codemirror/          # 代码编辑器
│   ├── css/                     # 自定义样式
│   ├── js/                      # 自定义脚本
│   └── index.html               # 主页面
├── src/                         # 服务器代码
├── configs/                     # 配置文件存储
├── download-assets.js           # 依赖下载脚本
├── 下载依赖.bat                 # Windows批处理
├── download-deps.sh             # Linux/Mac脚本
└── package.json
```

## 🌐 访问方式

启动成功后，您可以通过以下方式访问：

- **本机访问**：http://localhost:3000
- **局域网访问**：http://[您的IP地址]:3000

### 获取IP地址
- **Windows**：`ipconfig`
- **Linux/Mac**：`ifconfig` 或 `ip addr`

## ✅ 功能特性

- ✨ **完全离线运行**：所有依赖文件本地化
- 🌍 **局域网访问**：支持多设备同时使用
- 💾 **配置持久化**：服务器端存储，所有用户共享
- 🎨 **现代界面**：Bootstrap + CodeMirror
- 🔧 **实时编辑**：YAML配置实时预览

## 🛠️ 故障排除

### 下载依赖失败
```bash
# 检查网络连接
ping google.com

# 重新下载
npm run download-deps
```

### 局域网无法访问
1. 检查防火墙设置
2. 确认端口3000未被占用
3. 确认设备在同一网络

### 页面样式异常
1. 确认依赖文件已下载：检查 `public/libs/` 目录
2. 清除浏览器缓存
3. 检查控制台错误信息

## 📋 系统要求

- **Node.js**：14.0 或更高版本
- **内存**：至少 512MB
- **存储**：至少 100MB 可用空间
- **网络**：首次部署需要互联网连接

## 🔧 高级配置

### 修改端口
编辑 `src/index.js` 文件：
```javascript
const PORT = process.env.PORT || 3000; // 修改为其他端口
```

### 更新依赖版本
编辑 `download-assets.js` 文件中的版本号，然后重新运行下载脚本。

## 📞 技术支持

如遇问题，请检查：
1. Node.js 版本兼容性
2. 网络连接状态
3. 防火墙和端口设置
4. 依赖文件完整性

---

**🎉 部署完成后，您就可以在局域网内任意设备上使用Clash配置生成工具了！**
