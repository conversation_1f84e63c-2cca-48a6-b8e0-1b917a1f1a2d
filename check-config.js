const yaml = require('js-yaml');
const fs = require('fs');

function checkConfig() {
    console.log('🔍 开始检查 Clash 配置文件...\n');
    
    try {
        // 读取配置文件
        const configContent = fs.readFileSync('configs/SLEEP2025.yaml', 'utf8');
        const config = yaml.load(configContent);
        
        let hasErrors = false;
        
        // 1. 检查基本结构
        console.log('=== 1. 基本结构检查 ===');
        const requiredSections = ['proxies', 'proxy-groups', 'rules'];
        requiredSections.forEach(section => {
            if (config[section]) {
                console.log(`✅ ${section} 部分存在`);
            } else {
                console.log(`❌ ${section} 部分缺失`);
                hasErrors = true;
            }
        });
        
        // 2. 检查代理节点
        console.log('\n=== 2. 代理节点检查 ===');
        const proxies = config.proxies || [];
        console.log(`📊 总节点数: ${proxies.length}`);
        
        // 检查重复节点名称
        const names = proxies.map(p => p.name);
        const duplicateNames = names.filter((name, index) => names.indexOf(name) !== index);
        if (duplicateNames.length > 0) {
            console.log(`❌ 发现 ${duplicateNames.length} 个重复节点名称:`);
            [...new Set(duplicateNames)].forEach(name => console.log(`   - ${name}`));
            hasErrors = true;
        } else {
            console.log('✅ 节点名称无重复');
        }
        
        // 检查节点配置完整性
        let incompleteNodes = [];
        proxies.forEach((proxy, index) => {
            const missing = [];
            if (!proxy.name) missing.push('name');
            if (!proxy.type) missing.push('type');
            if (!proxy.server) missing.push('server');
            if (!proxy.port) missing.push('port');
            
            if (missing.length > 0) {
                incompleteNodes.push(`节点${index + 1} (${proxy.name || '未命名'}): 缺少 ${missing.join(', ')}`);
            }
        });
        
        if (incompleteNodes.length > 0) {
            console.log(`❌ 发现 ${incompleteNodes.length} 个不完整节点:`);
            incompleteNodes.forEach(node => console.log(`   - ${node}`));
            hasErrors = true;
        } else {
            console.log('✅ 所有节点配置完整');
        }
        
        // 检查节点类型分布
        const typeCount = {};
        proxies.forEach(proxy => {
            typeCount[proxy.type] = (typeCount[proxy.type] || 0) + 1;
        });
        console.log('📈 节点类型分布:');
        Object.entries(typeCount).forEach(([type, count]) => {
            console.log(`   - ${type}: ${count} 个`);
        });
        
        // 3. 检查代理组
        console.log('\n=== 3. 代理组检查 ===');
        const proxyGroups = config['proxy-groups'] || [];
        console.log(`📊 代理组数量: ${proxyGroups.length}`);
        
        proxyGroups.forEach((group, index) => {
            if (!group.name) {
                console.log(`❌ 代理组${index + 1} 缺少名称`);
                hasErrors = true;
            }
            if (!group.type) {
                console.log(`❌ 代理组 "${group.name}" 缺少类型`);
                hasErrors = true;
            }
            if (!group.proxies || group.proxies.length === 0) {
                console.log(`❌ 代理组 "${group.name}" 没有代理节点`);
                hasErrors = true;
            }
        });
        
        if (!hasErrors) {
            console.log('✅ 代理组配置正确');
        }
        
        // 4. 检查规则
        console.log('\n=== 4. 规则检查 ===');
        const rules = config.rules || [];
        console.log(`📊 规则数量: ${rules.length}`);
        
        // 检查重复规则
        const duplicateRules = rules.filter((rule, index) => rules.indexOf(rule) !== index);
        if (duplicateRules.length > 0) {
            console.log(`❌ 发现 ${duplicateRules.length} 个重复规则:`);
            [...new Set(duplicateRules)].slice(0, 5).forEach(rule => console.log(`   - ${rule}`));
            if (duplicateRules.length > 5) {
                console.log(`   ... 还有 ${duplicateRules.length - 5} 个重复规则`);
            }
            hasErrors = true;
        } else {
            console.log('✅ 规则无重复');
        }
        
        // 检查MATCH规则
        const matchRules = rules.filter(rule => rule.includes('MATCH'));
        console.log(`📋 MATCH规则数量: ${matchRules.length}`);
        if (matchRules.length > 1) {
            console.log('❌ 发现多个MATCH规则:');
            matchRules.forEach(rule => console.log(`   - ${rule}`));
            hasErrors = true;
        } else if (matchRules.length === 1) {
            console.log(`✅ MATCH规则正确: ${matchRules[0]}`);
        } else {
            console.log('⚠️  没有MATCH规则');
        }
        
        // 检查规则格式
        let invalidRules = [];
        rules.forEach((rule, index) => {
            if (typeof rule !== 'string' || !rule.includes(',')) {
                invalidRules.push(`规则${index + 1}: ${rule}`);
            }
        });
        
        if (invalidRules.length > 0) {
            console.log(`❌ 发现 ${invalidRules.length} 个格式错误的规则:`);
            invalidRules.slice(0, 5).forEach(rule => console.log(`   - ${rule}`));
            hasErrors = true;
        } else {
            console.log('✅ 规则格式正确');
        }
        
        // 5. 总结
        console.log('\n=== 📋 检查总结 ===');
        if (hasErrors) {
            console.log('❌ 配置文件存在问题，请修复上述错误');
            return false;
        } else {
            console.log('🎉 配置文件检查通过，没有发现问题！');
            console.log(`📊 统计信息:`);
            console.log(`   - 代理节点: ${proxies.length} 个`);
            console.log(`   - 代理组: ${proxyGroups.length} 个`);
            console.log(`   - 规则: ${rules.length} 条`);
            return true;
        }
        
    } catch (error) {
        console.log('❌ 检查过程中出错:', error.message);
        return false;
    }
}

// 运行检查
checkConfig();
