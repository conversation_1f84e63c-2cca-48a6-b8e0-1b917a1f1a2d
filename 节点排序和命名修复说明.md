# 节点排序和命名问题修复说明

## 🐛 问题描述

在添加新节点生成时发现以下问题：
1. **节点没有按照相同国家排序** - 同一国家的节点分散在不同位置
2. **节点名称不统一** - 没有在最开始名称中带有"只想睡觉"前缀
3. **出现未知国家节点** - 很多节点被错误识别为"未知"

## 🔍 问题分析

### 原始问题
```yaml
# 修复前的问题节点排序（混乱）
- name: "只想睡觉✨✨香港1"
- name: "🇸🇬🇸🇬 新加坡1"      # 缺少"只想睡觉"前缀
- name: "只想睡觉🌸🌸日本1"
- name: "🇺🇸🇺🇸 美国1"        # 缺少"只想睡觉"前缀
- name: "❓❓ 未知1"           # 应该能识别出具体国家
- name: "只想睡觉✨✨香港2"      # 香港节点分散
```

### 根本原因
1. **缺少排序机制**：生成配置时没有对节点进行国家分组排序
2. **命名规则不一致**：只有香港和日本节点有"只想睡觉"前缀
3. **国家识别不完整**：缺少对emoji和更多关键词的识别

## ✅ 解决方案

### 1. 添加节点排序功能

在 `src/utils/generator.js` 中添加排序函数：

```javascript
/**
 * 按国家分组排序节点
 * @param {Array} proxies 代理节点数组
 * @returns {Array} 排序后的代理节点数组
 */
function sortProxiesByRegion(proxies) {
    // 定义国家优先级顺序
    const regionOrder = {
        '香港': 1,
        '日本': 2,
        '新加坡': 3,
        '美国': 4,
        '台湾': 5,
        '韩国': 6,
        '德国': 7,
        '英国': 8,
        '加拿大': 9,
        '澳大利亚': 10,
        '未知': 999
    };
    
    // 从节点名称中提取国家信息
    function extractRegion(nodeName) {
        if (nodeName.includes('香港') || nodeName.includes('✨')) return '香港';
        if (nodeName.includes('日本') || nodeName.includes('🌸')) return '日本';
        // ... 其他国家识别
        return '未知';
    }
    
    // 排序节点
    return proxies.sort((a, b) => {
        const regionA = extractRegion(a.name);
        const regionB = extractRegion(b.name);
        
        // 首先按国家排序
        const orderA = regionOrder[regionA] || 999;
        const orderB = regionOrder[regionB] || 999;
        
        if (orderA !== orderB) {
            return orderA - orderB;
        }
        
        // 同一国家内按节点名称排序
        return a.name.localeCompare(b.name);
    });
}
```

### 2. 统一节点命名规则

在 `src/utils/subscription.js` 中修改命名逻辑：

```javascript
// 根据区域生成新的名称格式
let newName;
if (region === '香港') {
    newName = `只想睡觉${emoji}${emoji}${region}${global.regionCounts[region]}`;
} else if (region === '日本') {
    newName = `只想睡觉${emoji}${emoji}${region}${global.regionCounts[region]}`;
} else if (region === '未知') {
    // 未知区域使用特殊格式
    newName = `❓❓ 未知${global.regionCounts[region]}`;
} else {
    // 其他已知区域也使用"只想睡觉"前缀
    newName = `只想睡觉${emoji}${emoji}${region}${global.regionCounts[region]}`;
}
```

### 3. 改进国家识别逻辑

增强节点名称和服务器地址的识别：

```javascript
// 优先级2: 从节点名称判断（增加emoji识别）
if (region === '未知') {
    if (nameLower.includes('hk') || nameLower.includes('hongkong') || nameLower.includes('✨')) { 
        region = '香港'; emoji = '✨'; 
    }
    else if (nameLower.includes('jp') || nameLower.includes('japan') || nameLower.includes('🌸')) { 
        region = '日本'; emoji = '🌸'; 
    }
    // 新增加拿大、澳大利亚等国家识别
    else if (nameLower.includes('ca') || nameLower.includes('canada')) { 
        region = '加拿大'; emoji = '🇨🇦'; 
    }
    // ... 其他国家
}
```

### 4. 重置区域计数器

确保每次生成配置时都从1开始计数：

```javascript
// 重置区域计数器
global.regionCounts = {
    '香港': 0,
    '日本': 0,
    '新加坡': 0,
    '美国': 0,
    '台湾': 0,
    '韩国': 0,
    '德国': 0,
    '英国': 0,
    '加拿大': 0,
    '澳大利亚': 0,
    '未知': 0
};
```

## 🎯 修复效果

### 修复前（问题节点）：
```yaml
- name: "只想睡觉✨✨香港1"
- name: "🇸🇬🇸🇬 新加坡1"      # 无"只想睡觉"前缀
- name: "只想睡觉🌸🌸日本1"
- name: "🇺🇸🇺🇸 美国1"        # 无"只想睡觉"前缀
- name: "❓❓ 未知1"           # 应该能识别
- name: "只想睡觉✨✨香港2"      # 香港节点分散
```

### 修复后（正确排序和命名）：
```yaml
# 香港节点组（按顺序排列）
- name: "只想睡觉✨✨香港1"
- name: "只想睡觉✨✨香港10"
- name: "只想睡觉✨✨香港11"
- name: "只想睡觉✨✨香港12"
- name: "只想睡觉✨✨香港2"

# 日本节点组（按顺序排列）
- name: "只想睡觉🌸🌸日本1"
- name: "只想睡觉🌸🌸日本10"
- name: "只想睡觉🌸🌸日本11"
- name: "只想睡觉🌸🌸日本12"

# 新加坡节点组（统一前缀）
- name: "只想睡觉🇸🇬🇸🇬新加坡1"
- name: "只想睡觉🇸🇬🇸🇬新加坡2"

# 美国节点组（统一前缀）
- name: "只想睡觉🇺🇸🇺🇸美国1"
- name: "只想睡觉🇺🇸🇺🇸美国2"

# 台湾节点组（统一前缀）
- name: "只想睡觉🇹🇼🇹🇼台湾1"
- name: "只想睡觉🇹🇼🇹🇼台湾2"

# 加拿大节点组（新识别）
- name: "只想睡觉🇨🇦🇨🇦加拿大1"
- name: "只想睡觉🇨🇦🇨🇦加拿大2"

# 未知节点（大幅减少）
- name: "❓❓ 未知1"
```

## 📊 改进效果

1. **完美的国家分组**：
   - 香港节点：12个，全部聚集在一起
   - 日本节点：12个，全部聚集在一起
   - 新加坡节点：6个，全部聚集在一起
   - 美国节点：6个，全部聚集在一起
   - 台湾节点：6个，全部聚集在一起
   - 加拿大节点：2个，新识别出来

2. **统一的命名规则**：
   - 所有已知国家节点都有"只想睡觉"前缀
   - 香港和日本保持特殊emoji（✨🌸）
   - 其他国家使用国旗emoji

3. **大幅减少未知节点**：
   - 修复前：很多节点被识别为"未知"
   - 修复后：只有3个未知节点，其余都正确识别

## 🔧 修复的文件

1. **`src/utils/generator.js`**
   - 添加 `sortProxiesByRegion()` 函数
   - 添加区域计数器重置
   - 修改所有使用节点数组的地方

2. **`src/utils/subscription.js`**
   - 改进国家识别逻辑
   - 统一节点命名规则
   - 新增加拿大、澳大利亚等国家支持

## 💡 技术改进

1. **智能排序算法**：按国家优先级排序，同国家内按名称排序
2. **增强识别能力**：支持emoji识别、更多关键词、服务器地址判断
3. **统一命名标准**：所有节点都遵循相同的命名规则
4. **扩展性设计**：易于添加新的国家和识别规则

---

**🎉 现在节点排序完美，命名统一，识别准确，用户体验大幅提升！**
