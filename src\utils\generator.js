const fs = require('fs');
const path = require('path');
const yaml = require('js-yaml');
const { fetchProxiesFromSubscription } = require('./subscription');

/**
 * 验证代理节点配置的完整性
 * @param {Object} proxy 代理节点对象
 * @returns {boolean} 是否有效
 */
function validateProxy(proxy) {
    if (!proxy || !proxy.name || !proxy.type || !proxy.server || !proxy.port) {
        return false;
    }

    switch (proxy.type) {
        case 'ss':
        case 'ssr':
            return !!(proxy.cipher && proxy.password);
        case 'vmess':
            return !!(proxy.uuid);
        case 'trojan':
            return !!(proxy.password);
        default:
            console.warn(`未知的代理类型: ${proxy.type}`);
            return false;
    }
}

/**
 * 按国家分组排序节点
 * @param {Array} proxies 代理节点数组
 * @returns {Array} 排序后的代理节点数组
 */
function sortProxiesByRegion(proxies) {
    // 定义国家优先级顺序
    const regionOrder = {
        '香港': 1,
        '日本': 2,
        '新加坡': 3,
        '美国': 4,
        '台湾': 5,
        '韩国': 6,
        '德国': 7,
        '英国': 8,
        '加拿大': 9,
        '澳大利亚': 10,
        '未知': 999
    };

    // 从节点名称中提取国家信息
    function extractRegion(nodeName) {
        if (nodeName.includes('香港') || nodeName.includes('✨')) return '香港';
        if (nodeName.includes('日本') || nodeName.includes('🌸')) return '日本';
        if (nodeName.includes('新加坡') || nodeName.includes('🇸🇬')) return '新加坡';
        if (nodeName.includes('美国') || nodeName.includes('🇺🇸')) return '美国';
        if (nodeName.includes('台湾') || nodeName.includes('🇹🇼')) return '台湾';
        if (nodeName.includes('韩国') || nodeName.includes('🇰🇷')) return '韩国';
        if (nodeName.includes('德国') || nodeName.includes('🇩🇪')) return '德国';
        if (nodeName.includes('英国') || nodeName.includes('🇬🇧')) return '英国';
        if (nodeName.includes('加拿大') || nodeName.includes('🇨🇦')) return '加拿大';
        if (nodeName.includes('澳大利亚') || nodeName.includes('🇦🇺')) return '澳大利亚';
        return '未知';
    }

    // 从节点名称中提取数字编号
    function extractNumber(nodeName) {
        const match = nodeName.match(/(\d+)$/);
        return match ? parseInt(match[1]) : 0;
    }

    // 排序节点
    return proxies.sort((a, b) => {
        const regionA = extractRegion(a.name);
        const regionB = extractRegion(b.name);

        // 首先按国家排序
        const orderA = regionOrder[regionA] || 999;
        const orderB = regionOrder[regionB] || 999;

        if (orderA !== orderB) {
            return orderA - orderB;
        }

        // 同一国家内按数字编号排序
        const numberA = extractNumber(a.name);
        const numberB = extractNumber(b.name);

        return numberA - numberB;
    });
}

/**
 * 生成SLEEP2025.yaml配置文件
 * @param {string} header 用户自定义的头部配置
 * @param {Object} rules 用户自定义的规则
 * @param {Array} enabledSubscriptions 启用的订阅源
 * @param {Array} customNodes 自定义节点
 * @returns {Promise<string>} 生成的配置文件路径
 */
async function generateConfig(header, rules, enabledSubscriptions, customNodes = []) {
    console.log('开始生成SLEEP2025.yaml配置文件');

    // 重置节点计数器，确保每次生成配置文件时节点编号从1开始
    global.regionCounts = {
        '香港': 0,
        '日本': 0,
        '新加坡': 0,
        '美国': 0,
        '台湾': 0,
        '韩国': 0,
        '德国': 0,
        '英国': 0,
        '未知': 0
    };

    // 重置区域计数器
    global.regionCounts = {
        '香港': 0,
        '日本': 0,
        '新加坡': 0,
        '美国': 0,
        '台湾': 0,
        '韩国': 0,
        '德国': 0,
        '英国': 0,
        '加拿大': 0,
        '澳大利亚': 0,
        '未知': 0
    };

    // 获取所有订阅节点
    const allProxies = [];
    for (const subscription of enabledSubscriptions) {
        try {
            const proxies = await fetchProxiesFromSubscription(subscription);
            // 验证和过滤节点
            const validProxies = proxies.filter(proxy => {
                const isValid = validateProxy(proxy);
                if (!isValid) {
                    console.warn(`跳过无效节点: ${proxy.name || '未知'} (类型: ${proxy.type || '未知'})`);
                }
                return isValid;
            });

            allProxies.push(...validProxies);
            console.log(`从订阅 ${subscription} 获取了 ${proxies.length} 个节点，其中 ${validProxies.length} 个有效`);
        } catch (error) {
            console.error(`获取订阅 ${subscription} 失败:`, error.message);
            // 继续处理下一个订阅
        }
    }

    console.log(`总共获取了 ${allProxies.length} 个有效节点`);

    // 添加自定义节点
    if (customNodes && Array.isArray(customNodes) && customNodes.length > 0) {
        console.log(`添加 ${customNodes.length} 个自定义节点`);

        customNodes.forEach((customNode, index) => {
            try {
                // 验证自定义节点
                if (!validateProxy(customNode)) {
                    console.warn(`跳过无效的自定义节点 ${index + 1}: ${customNode.name || '未知'}`);
                    return;
                }

                // 为自定义节点生成名称
                const processedNode = processCustomNode(customNode);
                if (processedNode) {
                    allProxies.push(processedNode);
                    console.log(`添加自定义节点: ${processedNode.name}`);
                }
            } catch (error) {
                console.error(`处理自定义节点 ${index + 1} 失败:`, error.message);
            }
        });

        console.log(`总共节点数（包含自定义）: ${allProxies.length}`);
    }

    if (allProxies.length === 0) {
        throw new Error('没有获取到任何有效的代理节点');
    }

    // 过滤掉未知节点
    const knownProxies = allProxies.filter(proxy => {
        const isUnknown = proxy.name.includes('❓') || proxy.name.includes('未知');
        if (isUnknown) {
            console.log(`跳过未知节点: ${proxy.name}`);
        }
        return !isUnknown;
    });

    console.log(`过滤后剩余 ${knownProxies.length} 个已知国家节点`);

    // 按国家分组排序节点
    const sortedProxies = sortProxiesByRegion(knownProxies);
    console.log(`节点已按国家分组排序`);

    // 生成北京时间
    const now = new Date();
    const beijingTime = new Date(now.getTime() + (8 * 60 * 60 * 1000));
    const formattedTime = beijingTime.toISOString().replace('T', ' ').substring(0, 19);

    // 生成节点注释
    let nodeListComment = "# Proxies:\n";
    sortedProxies.forEach(proxy => {
        nodeListComment += `# - ${proxy.name}\n`;
    });

    // 构建最终配置
    let finalConfig = `# File Generated: ${formattedTime} (Beijing Time)\n`;
    finalConfig += `# SLEEP2025 - Made with Love\n\n`;

    // 1. 添加头部配置（如果有）
    if (header && header.trim()) {
        try {
            // 验证和格式化YAML
            const headerObj = yaml.load(header);
            finalConfig += yaml.dump(headerObj, { lineWidth: -1 });
        } catch (error) {
            console.error('头部YAML格式错误，使用原始内容:', error.message);
            finalConfig += header.trim() + '\n\n';
        }
    }

    // 2. 添加代理节点列表
    finalConfig += `proxies:\n`;
    sortedProxies.forEach(proxy => {
        try {
            // 安全地转义节点名称
            const safeName = proxy.name.replace(/"/g, '\\"');
            finalConfig += `  - name: "${safeName}"\n`;
            finalConfig += `    type: ${proxy.type}\n`;
            finalConfig += `    server: ${proxy.server}\n`;
            finalConfig += `    port: ${proxy.port}\n`;

            if (proxy.type === 'ss' || proxy.type === 'ssr') {
                finalConfig += `    cipher: ${proxy.cipher || 'aes-256-gcm'}\n`;
                finalConfig += `    password: "${proxy.password.replace(/"/g, '\\"')}"\n`;

                if (proxy.plugin) {
                    finalConfig += `    plugin: ${proxy.plugin}\n`;
                    if (proxy['plugin-opts']) {
                        finalConfig += `    plugin-opts:\n`;
                        for (const key in proxy['plugin-opts']) {
                            const value = proxy['plugin-opts'][key];
                            if (typeof value === 'string') {
                                finalConfig += `      ${key}: "${value.replace(/"/g, '\\"')}"\n`;
                            } else {
                                finalConfig += `      ${key}: ${value}\n`;
                            }
                        }
                    }
                }
            } else if (proxy.type === 'vmess') {
                finalConfig += `    uuid: ${proxy.uuid}\n`;
                finalConfig += `    alterId: ${proxy.alterId || 0}\n`;
                finalConfig += `    cipher: ${proxy.cipher || 'auto'}\n`;

                if (proxy.tls !== undefined) finalConfig += `    tls: ${proxy.tls}\n`;
                if (proxy.network) finalConfig += `    network: ${proxy.network}\n`;

                // 处理ws-path和ws-headers
                if (proxy['ws-path']) finalConfig += `    ws-path: "${proxy['ws-path'].replace(/"/g, '\\"')}"\n`;
                if (proxy['ws-headers']) {
                    finalConfig += `    ws-headers:\n`;
                    for (const key in proxy['ws-headers']) {
                        finalConfig += `      ${key}: "${proxy['ws-headers'][key].replace(/"/g, '\\"')}"\n`;
                    }
                }

                if (proxy.sni) finalConfig += `    sni: ${proxy.sni}\n`;
            } else if (proxy.type === 'trojan') {
                const password = proxy.password || 'default-password';
                finalConfig += `    password: "${password.replace(/"/g, '\\"')}"\n`;

                // trojan默认配置
                finalConfig += `    tls: ${proxy.tls !== undefined ? proxy.tls : true}\n`;
                finalConfig += `    skip-cert-verify: ${proxy['skip-cert-verify'] !== undefined ? proxy['skip-cert-verify'] : false}\n`;

                if (proxy.sni) finalConfig += `    sni: ${proxy.sni}\n`;
                if (proxy.network) finalConfig += `    network: ${proxy.network}\n`;

                // 处理ws相关配置
                if (proxy['ws-path']) finalConfig += `    ws-path: "${proxy['ws-path'].replace(/"/g, '\\"')}"\n`;
                if (proxy['ws-headers']) {
                    finalConfig += `    ws-headers:\n`;
                    for (const key in proxy['ws-headers']) {
                        finalConfig += `      ${key}: "${proxy['ws-headers'][key].replace(/"/g, '\\"')}"\n`;
                    }
                }
            }

            finalConfig += '\n';
        } catch (error) {
            console.error(`生成节点配置失败: ${proxy.name}`, error);
            // 跳过有问题的节点，继续处理下一个
        }
    });

    // 3. 检查头部中是否已有proxy-groups
    let hasProxyGroups = false;
    if (header) {
        try {
            const headerObj = yaml.load(header);
            hasProxyGroups = headerObj && headerObj['proxy-groups'] && Array.isArray(headerObj['proxy-groups']);
        } catch (e) {
            // 如果YAML解析失败，假设没有proxy-groups
            hasProxyGroups = false;
        }
    }

    // 如果头部没有定义proxy-groups，添加固定的"只想睡觉"分组
    if (!hasProxyGroups) {
        finalConfig += `proxy-groups:\n`;
        finalConfig += `  - name: 只想睡觉\n`;
        finalConfig += `    type: select\n`;
        finalConfig += `    proxies:\n`;
        finalConfig += `      - DIRECT\n`;

        // 添加所有节点到"只想睡觉"组
        sortedProxies.forEach(proxy => {
            finalConfig += `      - "${proxy.name}"\n`;
        });

        finalConfig += '\n';
    }

    // 4. 添加规则（带智能去重处理）
    finalConfig += `rules:\n`;

    // 收集所有规则并智能去重
    const ruleMap = new Map(); // 使用Map来处理域名冲突
    const comments = [];
    let hasMatchRule = false;
    let originalRuleCount = 0;
    let userRuleCount = 0;
    let existingRuleCount = 0;

    // 解析规则，提取域名和策略
    function parseRule(ruleStr) {
        const parts = ruleStr.split(',');
        if (parts.length >= 2) {
            const type = parts[0];
            const domain = parts[1];
            const policy = parts[2] || '只想睡觉';
            return { type, domain, policy, original: ruleStr };
        }
        return { original: ruleStr };
    }

    // 处理用户自定义规则（优先级高）
    if (rules && rules.userCustomRules) {
        const userRules = rules.userCustomRules.trim().split('\n');
        userRules.forEach(rule => {
            rule = rule.trim();
            originalRuleCount++;
            userRuleCount++;

            if (rule && rule.startsWith('#')) {
                // 保留注释
                comments.push(`  ${rule}`);
            } else if (rule) {
                // 处理规则
                let cleanRule = rule;
                if (cleanRule.startsWith("- '") || cleanRule.startsWith('-  ')) {
                    cleanRule = cleanRule.replace(/^-\s*/, '');
                }

                if (cleanRule.includes('MATCH')) {
                    hasMatchRule = true;
                }

                const parsed = parseRule(cleanRule);
                if (parsed.type && parsed.domain) {
                    const key = `${parsed.type},${parsed.domain}`;
                    ruleMap.set(key, { rule: cleanRule, source: 'user' });
                } else {
                    // 非标准规则直接添加
                    ruleMap.set(cleanRule, { rule: cleanRule, source: 'user' });
                }
            }
        });
    }

    // 处理已有规则（优先级低，如果用户规则中已有相同域名则跳过）
    if (rules && rules.existingRules) {
        const existingRules = rules.existingRules.trim().split('\n');
        existingRules.forEach(rule => {
            rule = rule.trim();
            originalRuleCount++;
            existingRuleCount++;

            if (rule && rule.startsWith('#')) {
                // 保留注释
                comments.push(`  ${rule}`);
            } else if (rule) {
                // 处理规则
                let cleanRule = rule;
                if (cleanRule.startsWith("- '") || cleanRule.startsWith('-  ')) {
                    cleanRule = cleanRule.replace(/^-\s*/, '');
                }

                if (cleanRule.includes('MATCH')) {
                    hasMatchRule = true;
                }

                const parsed = parseRule(cleanRule);
                if (parsed.type && parsed.domain) {
                    const key = `${parsed.type},${parsed.domain}`;
                    // 只有当用户规则中没有相同域名时才添加
                    if (!ruleMap.has(key)) {
                        ruleMap.set(key, { rule: cleanRule, source: 'existing' });
                    } else {
                        console.log(`跳过冲突规则: ${cleanRule} (用户规则优先)`);
                    }
                } else {
                    // 非标准规则，检查是否完全重复
                    if (!ruleMap.has(cleanRule)) {
                        ruleMap.set(cleanRule, { rule: cleanRule, source: 'existing' });
                    }
                }
            }
        });
    }

    // 添加注释
    comments.forEach(comment => {
        finalConfig += `${comment}\n`;
    });

    // 添加去重后的规则，但要确保MATCH规则在最后
    const finalRules = Array.from(ruleMap.values()).map(item => item.rule);

    // 分离MATCH规则和其他规则
    const matchRules = [];
    const otherRules = [];

    finalRules.forEach(rule => {
        if (rule.includes('MATCH')) {
            matchRules.push(rule);
        } else {
            otherRules.push(rule);
        }
    });

    // 先添加非MATCH规则（排序）
    const sortedOtherRules = otherRules.sort();
    sortedOtherRules.forEach(rule => {
        finalConfig += `  - ${rule}\n`;
    });

    // 最后添加MATCH规则
    if (matchRules.length > 0) {
        // 如果有多个MATCH规则，只保留第一个
        finalConfig += `  - ${matchRules[0]}\n`;
        console.log('用户规则中已包含MATCH规则，放置在最后');
        if (matchRules.length > 1) {
            console.log(`警告：发现${matchRules.length}个MATCH规则，只保留第一个`);
        }
    } else {
        // 如果没有MATCH规则，添加默认的MATCH规则
        finalConfig += `  - MATCH,只想睡觉\n`;
        console.log('添加默认MATCH规则');
    }

    const conflictCount = originalRuleCount - ruleMap.size;
    console.log(`规则处理完成:`);
    console.log(`  - 用户规则: ${userRuleCount} 条`);
    console.log(`  - 系统规则: ${existingRuleCount} 条`);
    console.log(`  - 去重前总计: ${originalRuleCount} 条`);
    console.log(`  - 去重后总计: ${ruleMap.size} 条`);
    console.log(`  - 去除重复/冲突: ${conflictCount} 条`);

    // 保存配置文件
    const configDir = path.join(__dirname, '../../configs');
    if (!fs.existsSync(configDir)) {
        fs.mkdirSync(configDir, { recursive: true });
    }

    const configFileName = 'SLEEP2025';
    const configFilePath = path.join(configDir, `${configFileName}.yaml`);
    fs.writeFileSync(configFilePath, finalConfig);

    console.log(`配置文件已生成: ${configFilePath}`);
    return configFileName;
}

/**
 * 处理自定义节点，生成名称并验证
 * @param {Object} customNode 自定义节点配置
 * @returns {Object|null} 处理后的节点或null
 */
function processCustomNode(customNode) {
    try {
        // 复制节点配置
        const processedNode = { ...customNode };

        // 根据服务器地址和类型确定区域
        const { region, emoji } = determineRegionFromCustomNode(customNode);

        // 增加该区域的计数
        if (!global.regionCounts) {
            global.regionCounts = {};
        }
        global.regionCounts[region] = (global.regionCounts[region] || 0) + 1;

        // 生成节点名称
        if (region === '未知') {
            processedNode.name = `❓❓ 自定义${global.regionCounts[region]}`;
        } else {
            processedNode.name = `只想睡觉${emoji}${emoji}${region}${global.regionCounts[region]}`;
        }

        return processedNode;
    } catch (error) {
        console.error('处理自定义节点失败:', error);
        return null;
    }
}

/**
 * 根据自定义节点确定区域
 * @param {Object} customNode 自定义节点
 * @returns {Object} 包含region和emoji的对象
 */
function determineRegionFromCustomNode(customNode) {
    const server = (customNode.server || '').toLowerCase();

    // 根据服务器地址判断区域
    if (server.includes('hk') || server.includes('hongkong') || server.includes('.hk')) {
        return { region: '香港', emoji: '✨' };
    } else if (server.includes('jp') || server.includes('japan') || server.includes('.jp')) {
        return { region: '日本', emoji: '🌸' };
    } else if (server.includes('sg') || server.includes('singapore') || server.includes('.sg')) {
        return { region: '新加坡', emoji: '🇸🇬' };
    } else if (server.includes('us') || server.includes('america') || server.includes('.us')) {
        return { region: '美国', emoji: '🇺🇸' };
    } else if (server.includes('tw') || server.includes('taiwan') || server.includes('.tw')) {
        return { region: '台湾', emoji: '🇹🇼' };
    } else if (server.includes('kr') || server.includes('korea') || server.includes('.kr')) {
        return { region: '韩国', emoji: '🇰🇷' };
    } else if (server.includes('de') || server.includes('germany') || server.includes('.de')) {
        return { region: '德国', emoji: '🇩🇪' };
    } else if (server.includes('uk') || server.includes('britain') || server.includes('.uk')) {
        return { region: '英国', emoji: '🇬🇧' };
    } else if (server.includes('ca') || server.includes('canada') || server.includes('.ca')) {
        return { region: '加拿大', emoji: '🇨🇦' };
    } else if (server.includes('au') || server.includes('australia') || server.includes('.au')) {
        return { region: '澳大利亚', emoji: '🇦🇺' };
    } else {
        // 根据IP段推测
        if (server.match(/^(45\.78\.|66\.112\.|103\.|119\.|202\.|203\.)/)) {
            return { region: '香港', emoji: '✨' };
        } else if (server.match(/^(104\.|128\.|172\.|192\.243\.|162\.248\.)/)) {
            return { region: '美国', emoji: '🇺🇸' };
        } else if (server.match(/^(93\.179\.|185\.|188\.)/)) {
            return { region: '德国', emoji: '🇩🇪' };
        } else {
            return { region: '未知', emoji: '❓' };
        }
    }
}

module.exports = {
    generateConfig
};