const axios = require('axios');

async function testCustomNodes() {
    try {
        console.log('开始测试自定义节点功能...');

        // 1. 首先保存一些测试配置
        const testConfig = {
            subscriptions: [
                {
                    url: 'https://example.com/test',
                    enabled: true,
                    name: '测试订阅'
                }
            ],
            header: 'port: 7890\nsocks-port: 7891\nallow-lan: true\nmode: rule\nlog-level: info',
            userRules: 'DOMAIN-SUFFIX,google.com,只想睡觉',
            existingRules: 'DOMAIN-SUFFIX,youtube.com,只想睡觉',
            customNodes: [
                {
                    type: 'trojan',
                    server: 'hk.example.com',
                    port: 443,
                    password: 'test123',
                    tls: true,
                    'skip-cert-verify': false
                },
                {
                    type: 'vmess',
                    server: 'jp.example.com',
                    port: 443,
                    uuid: '12345678-1234-1234-1234-123456789abc',
                    alterId: 0,
                    cipher: 'auto',
                    tls: true,
                    network: 'ws',
                    'ws-path': '/path'
                },
                {
                    type: 'ss',
                    server: 'us.example.com',
                    port: 8388,
                    cipher: 'aes-256-gcm',
                    password: 'testpass'
                }
            ]
        };

        console.log('保存测试配置...');
        const saveResponse = await axios.post('http://localhost:3000/api/save-config', testConfig);
        console.log('配置保存结果:', saveResponse.data);

        // 2. 生成配置文件（不使用订阅，只使用自定义节点）
        console.log('生成配置文件...');
        const generateResponse = await axios.post('http://localhost:3000/api/generate', {
            header: testConfig.header,
            rules: {
                userCustomRules: testConfig.userRules,
                existingRules: testConfig.existingRules
            },
            enabledSubscriptions: [], // 不使用订阅，避免404错误
            customNodes: testConfig.customNodes
        });

        console.log('配置生成结果:', generateResponse.data);

        // 3. 检查生成的配置文件
        if (generateResponse.data.success) {
            console.log('配置文件生成成功！');
            
            // 读取生成的配置文件
            const fs = require('fs');
            const path = require('path');
            const configPath = path.join(__dirname, 'configs', 'SLEEP2025.yaml');
            
            if (fs.existsSync(configPath)) {
                const configContent = fs.readFileSync(configPath, 'utf8');
                console.log('\n=== 生成的配置文件内容 ===');
                console.log(configContent);
                
                // 检查是否包含自定义节点
                const hasHKNode = configContent.includes('只想睡觉✨✨香港');
                const hasJPNode = configContent.includes('只想睡觉🌸🌸日本');
                const hasUSNode = configContent.includes('只想睡觉🇺🇸🇺🇸美国');
                
                console.log('\n=== 自定义节点检查结果 ===');
                console.log('香港节点:', hasHKNode ? '✅ 已包含' : '❌ 未包含');
                console.log('日本节点:', hasJPNode ? '✅ 已包含' : '❌ 未包含');
                console.log('美国节点:', hasUSNode ? '✅ 已包含' : '❌ 未包含');
                
                if (hasHKNode && hasJPNode && hasUSNode) {
                    console.log('\n🎉 测试成功！所有自定义节点都已正确包含在配置文件中！');
                } else {
                    console.log('\n❌ 测试失败！部分自定义节点未包含在配置文件中！');
                }
            } else {
                console.log('❌ 配置文件不存在:', configPath);
            }
        } else {
            console.log('❌ 配置文件生成失败:', generateResponse.data.error);
        }

    } catch (error) {
        console.error('测试失败:', error.message);
        if (error.response) {
            console.error('响应数据:', error.response.data);
        }
    }
}

// 运行测试
testCustomNodes();
